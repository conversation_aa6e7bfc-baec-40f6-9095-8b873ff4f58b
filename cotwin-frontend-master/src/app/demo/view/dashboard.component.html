<div class="grid dashboard">
    <div class="col-12 md:col-6 lg:col-3">
        <div class="grid grid-nogutter overview-box overview-box-1">
            <div class="col-4 overview-box-icon">
                <img src="assets/layout/images/dashboard/icon-1.svg"/>
            </div>
            <div class="col-8">
                <span class="overview-box-count">253</span>
                <span class="overview-box-name">FILES RECEIVED</span>
                <span class="overview-box-rate">+10%</span>
            </div>
        </div>
    </div>
    <div class="col-12 md:col-6 lg:col-3">
        <div class="grid grid-nogutter overview-box overview-box-2">
            <div class="col-4 overview-box-icon">
                <img src="assets/layout/images/dashboard/icon-2.svg"/>
            </div>
            <div class="col-8">
                <span class="overview-box-count">3216</span>
                <span class="overview-box-name">EMAILS SENT</span>
                <span class="overview-box-rate">+34%</span>
            </div>
        </div>
    </div>
    <div class="col-12 md:col-6 lg:col-3">
        <div class="grid grid-nogutter overview-box overview-box-3">
            <div class="col-4 overview-box-icon">
                <img src="assets/layout/images/dashboard/icon-3.svg"/>
            </div>
            <div class="col-8">
                <span class="overview-box-count">428</span>
                <span class="overview-box-name">CHECK-INS SAVED</span>
                <span class="overview-box-rate">-15%</span>
            </div>
        </div>
    </div>
    <div class="col-12 md:col-6 lg:col-3">
        <div class="grid grid-nogutter overview-box overview-box-4">
            <div class="col-4 overview-box-icon">
                <img src="assets/layout/images/dashboard/icon-4.svg"/>
            </div>
            <div class="col-8">
                <span class="overview-box-count">4</span>
                <span class="overview-box-name">NEW ORDERS</span>
                <span class="overview-box-rate">+24%</span>
            </div>
        </div>
    </div>

    <div class="col-12">
        <div class="grid grid-nogutter card overview-box control-panel">
            <div class="col-12 md:col-8 left-controls">
                <span>Some kind of control panel</span>
            </div>
            <div class="col-12 md:col-4 right-controls">
                <div class="grid grid-nogutter flex justify-content-end">
                    <div class="col-4 md:col-2">
                        <a href="#"><i class="pi pi-bookmark" aria-hidden="true"></i></a>
                    </div>
                    <div class="col-4 md:col-2">
                        <a href="#"><i class="pi pi-heart" aria-hidden="true"></i></a>
                    </div>
                    <div class="col-4 md:col-2">
                        <a href="#"><i class="pi pi-inbox" aria-hidden="true"></i></a>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="col-12 md:col-6 lg:col-8">
        <p-panel header="MAP" [style]="{'min-height':'360px'}">
            <div class="map"></div>
        </p-panel>
    </div>

    <div class="col-12 md:col-6 lg:col-4">
        <div class="card weather-card">
            <div class="grid grid-nogutter weather-box">
                <div class="col-12 md:col-6 left">
                    <div class="col-4 md:col-12">
                        <div class="large">8º</div>
                        <div class="normal">weather forecast</div>
                    </div>
                    <div class="col-4 md:col-12 stripe"></div>
                    <div class="col-4 md:col-12">
                        <div class="large">44%</div>
                        <div class="normal">hummidity</div>
                    </div>
                </div>
                <div class="col-12 md:col-6 right">
                    <div class="wrapper">
                        <div class="large">
                            Weather
                        </div>
                        <p>
                            The northern portion of the Rocky Mountains should expect some big snow totals coming out of this week’s storm system. All
                            of Idaho should expect snow for the middle to late part of this week. Starting in the southern portion
                            of the state, starting Wednesday, Sun Valley could see up to eight inches of fresh powder by Friday,
                            and Soldier Mountain will see much of the same. In the northern part of Idaho, the storm will hit Wednesday,
                            hammering ski areas like Lookout Pass, Magic Mountain, and Silver Mountain in the northern portion of
                            the state with anywhere from 12 to 20 inches.
                        </p>
                        <a href="">LEARN MORE</a>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="col-12 md:col-4">
        <div class="user-card">
            <div class="user-card-header"></div>
            <div class="user-card-content">
                <img src="assets/layout/images/avatar.png"/>
                <span class="user-card-name">Diana Davidson</span>
                <span class="user-card-role">System Admin</span>
                <p>
                    Retro occupy organic, stumptown shabby chic pour-over roof party DIY normcore.Actually artisan organic occupy, Wes Anderson ugh whatever pour-over gastropub selvage. Quis autem vel eum iure reprehenderit qui in ea voluptate velit esse.
                </p>
                <button type="button" label="Connect" icon="pi pi-check" class="raised-btn" pButton></button>
            </div>
            <div class="user-card-footer">
                <div class="grid grid-nogutter">
                    <div class="col-4">
                        <span>Issues</span>
                        <span>52</span>
                    </div>
                    <div class="col-4">
                        <span>Open</span>
                        <span>25</span>
                    </div>
                    <div class="col-4">
                        <span>Closed</span>
                        <span>27</span>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="col-12 md:col-8 chat">
        <p-panel header="Chat">
            <ul>
                <li class="clearfix message-from">
                    <img src="assets/layout/images/avatar2.png" />
                    <span>Retro occupy organic, stumptown shabby chic pour-over roof party DIY normcore.</span>
                </li>
                <li class="clearfix message-own">
                    <img src="assets/layout/images/avatar.png"/>
                    <span>Actually artisan organic occupy, Wes Anderson ugh whatever pour-over gastropub selvage.</span>
                </li>
                <li class="clearfix message-from">
                    <img src="assets/layout/images/avatar2.png"/>
                    <span>Chillwave craft beer tote bag stumptown quinoa hashtag.</span>
                </li>
                <li class="clearfix message-own">
                    <img src="assets/layout/images/avatar.png"/>
                    <span>Dreamcatcher locavore iPhone chillwave, occupy trust fund slow-carb distillery art party narwhal.</span>
                </li>
                <li class="clearfix message-own">
                    <span>Sed ut perspiciatis unde omnis iste natus.</span>
                </li>
                <li class="clearfix message-from">
                    <img src="assets/layout/images/avatar2.png"/>
                    <span>Quis autem vel eum iure reprehenderit qui in ea voluptate velit esse.</span>
                </li>
                <li class="clearfix message-own">
                    <img src="assets/layout/images/avatar.png"/>
                    <span>At vero eos et accusamus.</span>
                </li>
            </ul>
            <div class="new-message">
                <div class="message-attachment">
                    <i class="pi pi-paperclip"></i>
                </div>
                <div class="message-input">
                    <input type="text" placeholder="Write a message" />
                </div>
            </div>
        </p-panel>
    </div>

    <div class="col-12 md:col-6 lg:col-4 task-list">
        <p-panel header="Tasks" [style]="{'minHeight':'360px'}">
            <ul>
                <li>
                    <p-checkbox binary="true"></p-checkbox>
                    <span class="task-name">Sales Reports</span>
                    <i class="pi pi-briefcase"></i>
                </li>
                <li>
                    <p-checkbox binary="true"></p-checkbox>
                    <span class="task-name">Pay Invoices</span>
                    <i class="pi pi-money-bill"></i>
                </li>
                <li>
                    <p-checkbox binary="true"></p-checkbox>
                    <span class="task-name">Dinner with Tony</span>
                    <i class="pi pi-calendar"></i>
                </li>
                <li>
                    <p-checkbox binary="true"></p-checkbox>
                    <span class="task-name">Client Meeting</span>
                    <i class="pi pi-user"></i>
                </li>
                <li>
                    <p-checkbox binary="true"></p-checkbox>
                    <span class="task-name">New Theme</span>
                    <i class="pi pi-palette"></i>
                </li>
                <li>
                    <p-checkbox binary="true"></p-checkbox>
                    <span class="task-name">Flight Ticket</span>
                    <i class="pi pi-ticket"></i>
                </li>
                <li>
                    <p-checkbox binary="true"></p-checkbox>
                    <span class="task-name">Generate Charts</span>
                    <i class="pi pi-chart-bar"></i>
                </li>
                <li>
                    <p-checkbox binary="true"></p-checkbox>
                    <span class="task-name">New Theme</span>
                    <i class="pi pi-palette"></i>
                </li>
                <li>
                    <p-checkbox binary="true"></p-checkbox>
                    <span class="task-name">Flight Ticket</span>
                    <i class="pi pi-ticket"></i>
                </li>
            </ul>
        </p-panel>
    </div>
    <div class="col-12 md:col-6 lg:col-4 p-fluid contact-form">
        <p-panel header="Contact Us" [style]="{'minHeight':'360px'}">
            <div class="grid">
                <div class="col-12">
                    <p-dropdown [options]="cities" [(ngModel)]="selectedCity"></p-dropdown>
                </div>
                <div class="col-12">
                    <input type="text" pInputText placeholder="Name" >
                </div>
                <div class="col-12">
                    <input type="text" pInputText placeholder="Age" >
                </div>
                <div class="col-12">
                    <input type="text" pInputText placeholder="Email">
                </div>
                <div class="col-12">
                    <input type="text" pInputText placeholder="Location">
                </div>
                <div class="col-12">
                    <textarea type="text" pInputTextarea placeholder="Message"></textarea>
                </div>
            </div>
            <button type="button" label="Send" icon="pi pi-check" class="raised-btn" pButton></button>
        </p-panel>
    </div>
    <div class="col-12 lg:col-4 contacts">
        <p-panel header="Contacts" [style]="{'minHeight':'360px'}">
            <ul>
                <li class="clearfix">
                    <a href="#">
                        <img src="assets/layout/images/avatar1.png" width="45">
                        <div class="contact-info">
                            <span class="name">Joshua Williams</span>
                            <span class="location"><EMAIL></span>
                        </div>
                        <div class="contact-actions">
                            <span class="connection-status online">online</span>
                            <i class="pi pi-share-alt"></i>
                            <i class="pi pi-comment"></i>
                        </div>
                    </a>
                </li>
                <li class="clearfix">
                    <a href="#">
                        <img src="assets/layout/images/avatar2.png" width="45">
                        <div class="contact-info">
                            <span class="name">Emily Clark</span>
                            <span class="location"><EMAIL></span>
                        </div>
                        <div class="contact-actions">
                            <span class="connection-status offline">offline</span>
                            <i class="pi pi-share-alt"></i>
                            <i class="pi pi-comment"></i>
                        </div>
                    </a>
                </li>
                <li class="clearfix">
                    <a href="#">
                        <img src="assets/layout/images/avatar3.png" width="45">
                        <div class="contact-info">
                            <span class="name">Susan Johnson</span>
                            <span class="location"><EMAIL></span>
                        </div>
                        <div class="contact-actions">
                            <span class="connection-status offline">offline</span>
                            <i class="pi pi-share-alt"></i>
                            <i class="pi pi-comment"></i>
                        </div>
                    </a>
                </li>
                <li class="clearfix">
                    <img src="assets/layout/images/profile-image.png" width="45"/>
                    <div class="contact-info">
                        <span class="name">Jane White (me)</span>
                        <span class="location"><EMAIL></span>
                    </div>
                    <div class="contact-actions">
                        <span class="connection-status online">online</span>
                        <i class="pi pi-share-alt"></i>
                        <i class="pi pi-comment"></i>
                    </div>
                </li>
                <li class="clearfix">
                    <img src="assets/layout/images/avatar4.png" width="45"/>
                    <div class="contact-info">
                        <span class="name">David Stark</span>
                        <span class="location"><EMAIL></span>
                    </div>
                    <div class="contact-actions">
                        <span class="connection-status offline">offline</span>
                        <i class="pi pi-share-alt"></i>
                        <i class="pi pi-comment"></i>
                    </div>
                </li>
            </ul>
        </p-panel>
    </div>

    <div class="col-12 md:col-6">
        <div class="card morpheus-overview">
            <img src="assets/layout/images/dashboard/morpheus-photo.jpg"/>
            <span class="article-date">January 2017</span>
            <h3>Morpheus was here!</h3>
            <p>Greek mythology depicts its deities as belonging to one big family, and people today would likely be able to recognize its most prominent members. Most people are perhaps familiar with the Twelve Olympians, the major deities of the Greek pantheon. Many would have also heard about the Titans, the predecessors of the Olympian gods. Yet, the family tree of the Greek gods consists of more than just the Olympian gods and the Titans. There are gods whose existence predates even the Titans. One of these is Morpheus.</p>

            <p>In Greek mythology, Morpheus is a god of dreams. According to the Greeks, Morpheus was born of Nyx, the personification of Night. The Romans believe, however, that Morpheus was the son of Somnus, the personification of Sleep, who was in turn a child of Nyx. Regardless of his parentage, Morpheus is said to have numerous siblings, collectively known as the Oneiroi (the Greek word for dream, incidentally, being oneiros). Apart from Morpheus, two other Oneiroi can be identified by their names – Phobetor and Phantasos.</p>

            <p>Phobetor was thought to be the bringer of nightmares, and had the ability to appear as animals or monsters; Phantasos was believed to bring surreal and strange dreams, and was able to appear as inanimate objects, such as stones or wood. In contrast to his two siblings, Morpheus brought messages and prophesies from the gods to mortals through the medium of dreams. Thus, he appeared particularly to kings and heroes, and often took the appearance of a human being. When not appearing in dreams, Morpheus and his brothers were said to have possessed human forms with wings on their backs. These wings would have allowed Morpheus and his brethren to easily reach those whose dreams they were assigned. In addition, it is said that Morpheus’ wings enabled him to save his father Somnus, who was wingless, from the wrath of Zeus on more than one occasion.</p>
        </div>
    </div>

    <div class="col-12 md:col-6">
        <div class="card activity-feed">
            <p-panel header="ACTIVITY FEED">
                <h3>Last Activity</h3>
                <p>Updated 1 minute ago</p>
                <div class="grid">
                    <div class="col-12 md:col-6">
                        <span>INCOME</span>
                        <div class="knob income">$900</div>
                    </div>
                    <div class="col-12 md:col-6">
                        <span>TAX</span>
                        <div class="knob tax">$250</div>
                    </div>
                    <div class="col-12 md:col-6">
                        <span>INVOICES</span>
                        <div class="knob invoice">$125</div>
                    </div>
                    <div class="col-12 md:col-6">
                        <span>EXPENSES</span>
                        <div class="knob expense">$250</div>
                    </div>
                </div>
            </p-panel>
        </div>
    </div>

    <div class="col-12 md:col-4">
        <div class="card timeline p-fluid">
            <div class="grid">
                <div class="col-3">
                    <span class="event-time">just now</span>
                    <i class="pi pi-map-marker" style="color:#009688"></i>
                </div>
                <div class="col-9">
                    <span class="event-owner" style="color:#009688">Katherine May</span>
                    <span class="event-text">Lorem ipsun dolor amet</span>
                    <div class="event-content">
                        <img src="assets/layout/images/dashboard/bridge.png" width="100">
                    </div>
                </div>

                <div class="col-3">
                    <span class="event-time">12h ago</span>
                    <i class="pi pi-star" style="color:#f1b263"></i>
                </div>
                <div class="col-9">
                    <span class="event-owner" style="color:#f1b263">Brandon Santos</span>
                    <span class="event-text">Ab nobis, magnam sunt eum. Laudantium, repudiandae, similique!.</span>
                </div>

                <div class="col-3">
                    <span class="event-time">15h ago</span>
                    <i class="pi pi-comment" style="color:#2f8ee5"></i>
                </div>
                <div class="col-9">
                    <span class="event-owner" style="color:#2f8ee5">Stephan Ward</span>
                    <span class="event-text">Omnis veniam quibusdam ratione est repellat qui nam quisquam ab mollitia dolores ullam voluptates, similique, dignissimos.</span>
                </div>

                <div class="col-3">
                    <span class="event-time">2d ago</span>
                    <i class="pi pi-globe" style="color:#2f8ee5"></i>
                </div>
                <div class="col-9">
                    <span class="event-owner" style="color:#2f8ee5">Jason Smith</span>
                    <span class="event-text">Laudantium, repudiandae, similique!</span>
                    <div class="event-content">
                        <img src="assets/layout/images/dashboard/map.png" width="100">
                    </div>
                </div>

                <div class="col-3">
                    <span class="event-time">1w ago</span>
                    <i class="pi pi-heart" style="color:#6c76af"></i>
                </div>
                <div class="col-9">
                    <span class="event-owner">Kevin Cox</span>
                    <span class="event-text">Quibusdam ratione est repellat qui nam quisquam veniam quibusdam ratione.</span>
                </div>

                <div class="col-3">
                    <span class="event-time">2w ago</span>
                    <i class="pi pi-compass" style="color:#2d353c"></i>
                </div>
                <div class="col-9">
                    <span class="event-owner" style="color:#2d353c">Walter White</span>
                    <span class="event-text">I am the one who knocks!</span>
                </div>

                <div class="col-12">
                    <button type="button" label="Refresh" icon="pi pi-refresh" class="raised-btn" pButton></button>
                </div>
            </div>
        </div>
    </div>

    <div class="col-12 md:col-8">
        <div class="card">
            <p-table [value]="products" [paginator]="true" [rows]="5" styleClass="p-datatable-customers">
                <ng-template pTemplate="header">
                    <tr>
                        <th pSortableColumn="id">ID
                            <p-sortIcon field="id"></p-sortIcon>
                        </th>
                        <th pSortableColumn="category">Category
                            <p-sortIcon field="category"></p-sortIcon>
                        </th>
                        <th pSortableColumn="price">Price
                            <p-sortIcon field="price"></p-sortIcon>
                        </th>
                        <th pSortableColumn="inventoryStatus">Status
                            <p-sortIcon field="inventoryStatus"></p-sortIcon>
                        </th>
                        <th></th>
                    </tr>
                </ng-template>
                <ng-template pTemplate="body" let-product>
                    <tr>
                        <td>
                            <span class="p-column-title">Id</span>
                            {{product.id}}</td>
                        <td>
                            <span class="p-column-title">Category</span>
                            {{product.category}}</td>
                        <td>
                            <span class="p-column-title">Price</span>
                            {{product.price | currency:'USD'}}</td>
                        <td>
                            <span class="p-column-title">Status</span>
                            <span [class]="'product-badge status-' + product.inventoryStatus.toLowerCase()">{{product.inventoryStatus}}</span>
                        </td>
                        <td style="text-align: center">
                            <button pButton type="button" icon="pi pi-search"></button>
                        </td>
                    </tr>
                </ng-template>
            </p-table>

            <p-panel header="Sales Graph">
                <div style="overflow:auto">
                    <p-chart type="line" [data]="chartData" responsive="true"></p-chart>
                </div>
            </p-panel>
        </div>
    </div>

    <div class="col-12 md:col-8">
        <p-panel header="Calendar" [style]="{'height':'100%'}">
            <full-calendar [options]="fullcalendarOptions" ></full-calendar>
        </p-panel>
    </div>
    <div class="col-12 md:col-4">
        <p-panel header="Activity" [style]="{'height':'100%'}">
            <div class="activity-header">
                <div class="grid">
                    <div class="col-6">
                        <span style="font-weight:bold">Last Activity</span>
                        <p>Updated 1 minute ago</p>
                    </div>
                    <div class="col-6" style="text-align:right">
                        <button type="button" label="Update" icon="pi pi-refresh" class="raised-btn" pButton></button>
                    </div>
                </div>
            </div>
            <ul class="activity-list">
                <li>
                    <div class="count">$900</div>
                    <div class="grid">
                        <div class="col-6">Income</div>
                        <div class="col-6">95%</div>
                    </div>
                </li>
                <li>
                    <div class="count" style="background-color:#00acac">$250</div>
                    <div class="grid">
                        <div class="col-6">Tax</div>
                        <div class="col-6">24%</div>
                    </div>
                </li>
                <li>
                    <div class="count" style="background-color:#2f8ee5">$125</div>
                    <div class="grid">
                        <div class="col-6">Invoices</div>
                        <div class="col-6">55%</div>
                    </div>
                </li>
                <li>
                    <div class="count" style="background-color:#f1b263">$250</div>
                    <div class="grid">
                        <div class="col-6">Expenses</div>
                        <div class="col-6">15%</div>
                    </div>
                </li>
                <li>
                    <div class="count" style="background-color:#6c76af">$350</div>
                    <div class="grid">
                        <div class="col-6">Bonus</div>
                        <div class="col-6">5%</div>
                    </div>
                </li>
                <li>
                    <div class="count" style="background-color:#2f8ee5">$500</div>
                    <div class="grid">
                        <div class="col-6">Revenue</div>
                        <div class="col-6">25%</div>
                    </div>
                </li>
            </ul>
        </p-panel>
    </div>
</div>
