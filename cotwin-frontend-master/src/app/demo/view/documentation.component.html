<div class="grid">
    <div class="col-12">
        <div class="card docs">

            <h4>Current Version</h4>
            <p>Angular 13 and PrimeNG 13</p>

            <h4>Dependencies</h4>
            <p>Morpheus has no direct dependency. More information about dependencies is available at <a href="https://www.primefaces.org/why-primeng-templates/">Why PrimeNG Templates</a> article.</p>

            <h4>Getting Started</h4>
            <p>Morpheus is a true native application template for Angular and is distributed as a CLI project. If you don't have CLI installed already run the following commands to set it up. In case
            you have an application that do not use CLI, skip the <a href="#noncli">Integration with an Existing Non CLI Application</a> part.</p>
<app-code ngPreserveWhitespaces ngNonBindable lang="markup">
npm install -g @angular-cli
</app-code>

            <p>Once CLI is ready in your system, extract the contents of the morpheus zip file distribution, cd to the directory,
            install the libraries from npm and then execute "ng serve" to run the application in your local environment at http://localhost:4200/.</p>
<app-code ngPreserveWhitespaces ngNonBindable lang="markup">
cd morpheus
npm install
ng serve
</app-code>

            <p>That's it, you may now start with the development of your application.</p>

            <h4>Important CLI Commands</h4>
            <p>Following commands are derived from CLI.</p>
<app-code ngPreserveWhitespaces ngNonBindable lang="markup">
Run 'ng serve' for a dev server. Navigate to `http://localhost:4200/`. The app will automatically reload if you change any of the source files.

Run 'ng generate component component-name' to generate a new component. You can also use `ng generate directive/pipe/service/class/module`.

Run 'ng build' to build the project. The build artifacts will be stored in the `dist/` directory. Use the `-prod` flag for a production build.

Run 'ng test' to execute the unit tests via [Karma](https://karma-runner.github.io).

Run 'ng e2e' to execute the end-to-end tests via [Protractor](http://www.protractortest.org/).

Run 'ng help' for more options.
</app-code>

            <h4>Structure</h4>
            <p>Morpheus consists of 3 main parts; the application layout, layout resources and theme resources for PrimeNG components. <i>app.component.html</i> inside app folder is the html template for the base layout,
                required resources for the layout are placed inside the <i>src/assets/layout</i> folder and similarly theme resources are inside <i>src/assets/theme</i> folder.
            </p>

            <h4>Template</h4>
            <p>Main layout is the html view of the app.component.ts, it is divided into a couple of sections such as topbar, sidebar and footer. Here is the code for
                the main template. The component class app.component.ts implements the logic such as opening menus and layout modes.
            </p>
<app-code ngPreserveWhitespaces ngNonBindable>
&lt;div class="layout-wrapper" [ngClass]="&#123;'layout-wrapper-menu-active':sidebarActive,
                                        'layout-overlay-menu':overlay, 'p-input-filled': app.inputStyle === 'filled', 'p-ripple-disabled': !app.ripple&#125;"&gt;

    &lt;app-topbar&gt;&lt;/app-topbar&gt;

    &lt;app-sidebar&gt;&lt;/app-sidebar&gt;

    &lt;div class=&quot;layout-content&quot;&gt;
        &lt;div class=&quot;layout-main&quot;&gt;
            &lt;router-outlet&gt;&lt;/router-outlet&gt;
        &lt;/div&gt;

        &lt;app-footer&gt;&lt;/app-footer&gt;
    &lt;/div&gt;

    &lt;app-config&gt;&lt;/app-config&gt;

&lt;/div>
</app-code>

            <h4>Menu</h4>
            <p>Menu is a separate component defined in app.menu.component.ts file based on PrimeNG MenuModel API. In order to define the menuitems,
            navigate to app.menu.component.ts and define your own model. Here is the menu component from the sample application.</p>
<div style="height:400px;overflow: auto;">
<app-code ngPreserveWhitespaces ngNonBindable lang="javascript">
import &#123;Component, OnInit&#125; from '@angular/core';
import &#123;AppComponent&#125; from './app.component';

@Component(&#123;
    selector: 'app-menu',
    template: `
		&lt;ul class=&quot;navigation-menu&quot;&gt;
			&lt;li app-menuitem *ngFor=&quot;let item of model; let i = index;&quot; [item]=&quot;item&quot; [index]=&quot;i&quot; [root]=&quot;true&quot;&gt;&lt;/li&gt;
		&lt;/ul&gt;
    `
&#125;)
export class AppMenuComponent implements OnInit &#123;

    model: any[];

    constructor(public app: AppComponent) &#123;&#125;

    ngOnInit() &#123;
        this.model = [
            &#123;label: 'Dashboard', icon: 'pi pi-fw pi-home', routerLink: ['/']&#125;,
            &#123;
                label: 'UI Kit', icon: 'pi pi-fw pi-star', routerLink: ['/uikit'],
                items: [
                    &#123;label: 'Form Layout', icon: 'pi pi-fw pi-id-card', routerLink: ['/uikit/formlayout']&#125;,
                    &#123;label: 'Input', icon: 'pi pi-fw pi-check-square', routerLink: ['/uikit/input']&#125;,
                    &#123;label: 'Float Label', icon: 'pi pi-fw pi-bookmark', routerLink: ['/uikit/floatlabel']&#125;,
                    &#123;label: 'Invalid State', icon: 'pi pi-fw pi-exclamation-circle', routerLink: ['/uikit/invalidstate']&#125;,
                    &#123;label: 'Button', icon: 'pi pi-fw pi-mobile', routerLink: ['/uikit/button'], class: 'rotated-icon'&#125;,
                    &#123;label: 'Table', icon: 'pi pi-fw pi-table', routerLink: ['/uikit/table']&#125;,
                    &#123;label: 'List', icon: 'pi pi-fw pi-list', routerLink: ['/uikit/list']&#125;,
                    &#123;label: 'Tree', icon: 'pi pi-fw pi-share-alt', routerLink: ['/uikit/tree']&#125;,
                    &#123;label: 'Panel', icon: 'pi pi-fw pi-tablet', routerLink: ['/uikit/panel']&#125;,
                    &#123;label: 'Overlay', icon: 'pi pi-fw pi-clone', routerLink: ['/uikit/overlay']&#125;,
                    &#123;label: 'Media', icon: 'pi pi-fw pi-image', routerLink: ['/uikit/media']&#125;,
                    &#123;label: 'Menu', icon: 'pi pi-fw pi-bars', routerLink: ['/uikit/menu'], preventExact: true&#125;,
                    &#123;label: 'Message', icon: 'pi pi-fw pi-comment', routerLink: ['/uikit/message']&#125;,
                    &#123;label: 'File', icon: 'pi pi-fw pi-file', routerLink: ['/uikit/file']&#125;,
                    &#123;label: 'Chart', icon: 'pi pi-fw pi-chart-bar', routerLink: ['/uikit/charts']&#125;,
                    &#123;label: 'Misc', icon: 'pi pi-fw pi-circle-off', routerLink: ['/uikit/misc']&#125;
                ]
            &#125;,
            &#123;
                label: 'Prime Blocks', icon: 'pi pi-fw pi-prime', routerLink: ['/blocks'],
                items: [
                    &#123;label: 'Free Blocks', icon: 'pi pi-fw pi-eye', routerLink: ['/blocks']&#125;,
                    &#123;label: 'All Blocks', icon: 'pi pi-fw pi-globe', url: ['https://www.primefaces.org/primeblocks-ng'], target: '_blank'&#125;,
                ]
            &#125;,
            &#123;
                label: 'Utilities', icon: 'pi pi-fw pi-compass', routerLink: ['/utilities'],
                items: [
                    &#123;label: 'PrimeIcons', icon: 'pi pi-fw pi-prime', routerLink: ['utilities/icons']&#125;,
                    &#123;label: 'PrimeFlex', icon: 'pi pi-fw pi-desktop', url: ['https://www.primefaces.org/primeflex/'], target: '_blank'&#125;,
                ]
            &#125;,
            &#123;
                label: 'Pages', icon: 'pi pi-fw pi-briefcase', routerLink: ['/pages'],
                items: [
                    &#123;label: 'Crud', icon: 'pi pi-fw pi-pencil', routerLink: ['/pages/crud']&#125;,
                    &#123;label: 'Calendar', icon: 'pi pi-fw pi-calendar-plus', routerLink: ['/pages/calendar']&#125;,
                    &#123;label: 'Timeline', icon: 'pi pi-fw pi-calendar', routerLink: ['/pages/timeline']&#125;,
                    &#123;label: 'Landing', icon: 'pi pi-fw pi-globe', url: 'assets/pages/landing.html', target: '_blank'&#125;,
                    &#123;label: 'Login', icon: 'pi pi-fw pi-sign-in', routerLink: ['/login']&#125;,
                    &#123;label: 'Invoice', icon: 'pi pi-fw pi-dollar', routerLink: ['/pages/invoice']&#125;,
                    &#123;label: 'Help', icon: 'pi pi-fw pi-question-circle', routerLink: ['/pages/help']&#125;,
                    &#123;label: 'Error', icon: 'pi pi-fw pi-times-circle', routerLink: ['/error']&#125;,
                    &#123;label: 'Not Found', icon: 'pi pi-fw pi-exclamation-circle', routerLink: ['/notfound']&#125;,
                    &#123;label: 'Access Denied', icon: 'pi pi-fw pi-lock', routerLink: ['/access']&#125;,
                    &#123;label: 'Empty', icon: 'pi pi-fw pi-circle-off', routerLink: ['/pages/empty']&#125;
                ]
            &#125;,
            &#123;
                label: 'Hierarchy', icon: 'pi pi-fw pi-align-left',
                items: [
                    &#123;
                        label: 'Submenu 1', icon: 'pi pi-fw pi-align-left',
                        items: [
                            &#123;
                                label: 'Submenu 1.1', icon: 'pi pi-fw pi-align-left',
                                items: [
                                    &#123;label: 'Submenu 1.1.1', icon: 'pi pi-fw pi-align-left'&#125;,
                                    &#123;label: 'Submenu 1.1.2', icon: 'pi pi-fw pi-align-left'&#125;,
                                    &#123;label: 'Submenu 1.1.3', icon: 'pi pi-fw pi-align-left'&#125;,
                                ]
                            &#125;,
                            &#123;
                                label: 'Submenu 1.2', icon: 'pi pi-fw pi-align-left',
                                items: [
                                    &#123;label: 'Submenu 1.2.1', icon: 'pi pi-fw pi-align-left'&#125;
                                ]
                            &#125;,
                        ]
                    &#125;,
                    &#123;
                        label: 'Submenu 2', icon: 'pi pi-fw pi-align-left',
                        items: [
                            &#123;
                                label: 'Submenu 2.1', icon: 'pi pi-fw pi-align-left',
                                items: [
                                    &#123;label: 'Submenu 2.1.1', icon: 'pi pi-fw pi-align-left'&#125;,
                                    &#123;label: 'Submenu 2.1.2', icon: 'pi pi-fw pi-align-left'&#125;,
                                ]
                            &#125;,
                            &#123;
                                label: 'Submenu 2.2', icon: 'pi pi-fw pi-align-left',
                                items: [
                                    &#123;label: 'Submenu 2.2.1', icon: 'pi pi-fw pi-align-left'&#125;,
                                ]
                            &#125;,
                        ]
                    &#125;
                ]
            &#125;,
            &#123;
            label: 'Buy Now', icon: 'pi pi-fw pi-shopping-cart', url: ['https://www.primefaces.org/store']
            &#125;,
            &#123;
            label: 'Documentation', icon: 'pi pi-fw pi-info-circle', routerLink: ['/documentation']
            &#125;
        ];
    &#125;
&#125;
</app-code>
</div>

            <h4>Integration with an Existing CLI Project</h4>
            <p>To setup Morpheus in an existing project, copy the <i>src/assets</i> folder to your projects folder with the same name
                and replace the contents of app.component.ts, app.component.html with their counterparts in Morpheus under <i>src/app</i> folder.</p>

            <p>Install PrimeNG</p>
<app-code ngPreserveWhitespaces ngNonBindable lang="markup">
npm install primeng@latest --save
npm install primeicons@latest --save
</app-code>

            <p>Add PrimeNG CSS at styles section in angular.json.</p>

<app-code ngPreserveWhitespaces ngNonBindable lang="json">
"styles": [
    "styles.scss"           //your styles and overrides
],
</app-code>

            <p>Last part is adding theme and layout css files, in the CLI app they are defined using link tags in index.html so the demo can switch them on
            the fly by changing the path however if this is not a requirement, you may also add them to the styles configuration so they go inside the bundle.</p>

            <h4 id="noncli">Integration with an Existing Non-CLI Project</h4>
            <p>For an existing project that do not use CLI, setup steps are more or less similar. Start with installing the dependencies listed above in package.json</p>
            <p>Copy the <i>src/assets</i> folder to your application and include the resources listed above with a module bundler like webpack or using link-script tags.</p>
            <p>Finally copy the contents of app.component.html to your application's main component template such as <i>app/application.html</i> along
            with the sub components which are app.menu.component.ts, app.topbar.component.ts and app.footer.component.ts.</p>

            <h4>Theme</h4>
            <p>Morpheus provides 3 PrimeNG themes out of the box, setup of a theme simple including the css of theme to your page that are located inside assets/theme folder.</p>

            <ul>
                <li>theme-blue</li>
                <li>theme-green</li>
                <li>theme-orange</li>
            </ul>

            <p>A custom theme can be developed by the following steps.</p>
            <ul>
                <li>Choose a custom theme name such as theme-myown.</li>
                <li>Create a file named theme-myown.scss under <i>assets/theme folder</i>.</li>
                <li>Define the variables listed below and import the <i>/sass/theme/_theme.scss</i> file.</li>
                <li>Build the scss to generate css</li>
                <li>Include the generated theme.css to your page.</li>
            </ul>

            <p>Here are the variables required to create a theme, you may need to change the last line according to the
                relative path of the sass folder in your application.</p>

<app-code ngPreserveWhitespaces ngNonBindable lang="css">
$primaryColor: #61d42d !default;
$primaryLightColor: scale-color($primaryColor, $lightness: 60%) !default;
$primaryDarkColor: scale-color($primaryColor, $lightness: -10%) !default;
$primaryDarkerColor: scale-color($primaryColor, $lightness: -20%) !default;
$primaryTextColor: #ffffff !default;

$highlightBg: $primaryColor;
$highlightTextColor: $primaryTextColor;

@import '../sass/theme/_theme';
</app-code>

            <p> An example sass command to compile the css would be;</p>

<app-code ngPreserveWhitespaces ngNonBindable lang="markup">
sass src/assets/theme-myown/theme.scss src/assets/theme-myown/theme.css
</app-code>

            <p>Watch mode is handy to avoid compiling everytime when a change is made, instead use the following command
            so that sass generates the file whenever you make a customization. This builds all css files whenever a change is made to any scss file.</p>
<app-code ngPreserveWhitespaces ngNonBindable lang="markup">
sass --watch src/:src/ --no-source-map
</app-code>

            <p>Same can also be applied to layout itself;</p>
            <ul>
                <li>Choose a layout name such as layout-myown.</li>
                <li>Create an empty file named layout-myown.scss inside <i>assets/layout/css</i> folder.</li>
                <li>Define the variables listed below and import the <i>/sass/layout/_layout.scss</i> file.</li>
                <li>Build the scss to generate css</li>
                <li>Serve the css by importing it using a link tag or a bundler.</li>
            </ul>

            <p>Here are the variables required to create a layout, you may need to change the last line according to the
                relative path of the sass folder in your application.</p>

<app-code ngPreserveWhitespaces ngNonBindable lang="css">
$primaryColor: #6ec5ff;
$highlightTextColor:#ffffff;
$buttonTextColor:#ffffff;

@import '/../sass/layout/_layout';
</app-code>

            <p>For both cases, several .scss files such as _layout.scss, _theme.scss or _variables.scss has to be present relative to your scss files, these are available inside the assets/sass folder in the distribution.</p>
            <p>In case you'd like to customize the structure not just the colors, the _variables.scss is where the structural variables (e.g. font size, paddings) for the layout are defined.</p>
<app-code ngPreserveWhitespaces ngNonBindable lang="css">
$fontFamily: "Source Sans Pro","Helvetica Neue",sans-serif;
$fontSize:13px;
$textColor:#777777;
$textSecondaryColor:lighten($textColor,5%);
$borderRadius:3px;
$dividerColor:#eaeaea;
$iconFontSize:16px;
$iconWidth:16px;
$iconHeight:16px;
$transitionDuration:.3s;
$mainColor: #61d42d;

$bodyBgColor:#f8f8f8;
$footerBgColor:#dee0e3;
$footerLinkHoverColor: #494949;
$menuBgColor: #2C2C2C;
$menuBgColorActive: #1f1f1f;
$menuTextColor: #c4c4c4;

/* Topbar */
$topbarMenuTextColor:#ffffff;
$topbarMenuTextHoverColor:#e3e3e3;
$topbarSubmenuBgColor:#f7f7f7;
$topbarSubmenuItemHoverBgColor:#e3e3e3;
$topbarButtonHoverBgColor: #f5f5f5;
$topbarButtonHoverColor:#494949;
$topbarIconColor:#afafaf;
$topbarBgColor: #ffffff;

/* Tab */
$progressBarBgColor: #eeeeee;
$progressBarFirstColor: #92f5a1;
$progressBarSecondColor: #c4e3c9;
$progressBarThirdColor: #c8cfc9;
$progressBarForthColor: #dedede;

/* Contents */
$contentBorderColor:#eaeaea;

/* List Items */
$listItemPadding:.5em 1em;

/* Predefined Colors */
$yellow:#fbc948;
$blue:#6ec5ff;
$orange:#F1B009;
$purple:#985edb;
$pink:#f18983;
$green:#59c429;
$black:#777777;
$secondary:#f4f4f4;
</app-code>

            <h4>Menu Item Badges</h4>
            <p>Badges are numerical indicators of how many items are associated with a link.
               The badge property is value of the badge and badgeStyleClass is style class of the badge.</p>
<app-code ngPreserveWhitespaces ngNonBindable lang="json">
label: 'Components', icon: 'list', badge: '2', badgeStyleClass: 'red-badge'
</app-code>
            <p>Morpheus layout has 2 different badge style.There is a default badge which uses the primary color of morpheus layout. And there is 3 other badge class for menu items.</p>
            <ul>
                <li>orange-badge</li>
                <li>purple-badge</li>
                <li>blue-badge</li>
            </ul>

            <h4>Menu Modes</h4>
            <p>Menu has 2 modes; static and overlay. Layout container element in application.html is used to define which mode to use by adding specific classes. List
            below indicates the style classes for each mode.</p>
            <ul>
                <li>Static: "layout-wrapper"</li>
                <li>Overlay: "layout-wrapper layout-overlay-menu"</li>
            </ul>

            <p>It is also possible to leave the choice to the user by keeping the preference at a component and using an expression to bind it so that user can switch between modes. Sample
            application has an example implementation of such use case. Refer to app.component.ts for an example.</p>

            <h4>Grid CSS</h4>
            <p>Morpheus uses PrimeNG Flex Grid CSS throughout the demos such as Dashboard, however any Grid library can be used with it since Morpheus Layout itself does not depend on PrimeFlex CSS.</p>

            <h4>Customizing Styles</h4>
            <p>It is suggested to add your customizations in the following sass files under the override folder instead of adding them to the
                scss files under sass folder to avoid maintenance issues after an update.</p>

            <ul>
                <li><b>_layout_variables</b>: Variables of the layout.</li>
                <li><b>_layout_styles</b>: Styles for the layout.</li>
                <li><b>_theme_variables</b>: Variables of the theme.</li>
                <li><b>_theme_styles</b>: Styles for the theme.</li>
            </ul>

            <h4>Migration Guide</h4>
            <p>Every change is included in <b>CHANGELOG.md</b> file at the root folder of the distribution along with the instructions to update.</p>
        </div>
    </div>
</div>
