<div class="layout-sidebar" [ngStyle]="{'overflow':app.sidebarActive ? 'hidden' : 'visible'}" (click)="app.onSidebarClick($event)">
    <div class="layout-tabmenu">
        <ul class="layout-tabmenu-nav">
            <li *ngFor="let item of menuItems; let i = index;" [ngClass]="{'active-item':router.url.includes(item.routerLink)}">
              <ng-container *ngIf="(item.feature | featureCheck)">
                <a *ngIf="item.routerLink!=null" [routerLink]="item.routerLink" (click)="app.closeSidebar($event)" class="tabmenuitem-link"  [pTooltip]="item.label" pRipple>
                  <i [className]="item.icon"></i></a>
                <a *ngIf="item.routerLink===null" [routerLink]="item.routerLink" (click)="app.onTabClick($event, item.index)" class="tabmenuitem-link"  [pTooltip]="item.label" pRipple>
                  <i [className]="item.icon"></i></a>
                <div *ngIf="item.routerLink===null && item.index===app.activeTabIndex"  class="layout-tabmenu-contents">
                  <div class="layout-tabmenu-content" [ngClass]="{'layout-tabmenu-content-active': app.activeTabIndex === item.index}">
                    <div class="layout-submenu-title clearfix">
                      <span>{{item.label}}</span>
                    </div>
                    <app-sidebarTabContent>
                      <ul class="navigation-menu">
                        <ng-container *ngFor="let item of item.items; let i = index;" >
                          <li app-menuitem [item]="item" [index]="i" [root]="true"></li>
                        </ng-container>
                      </ul>
                    </app-sidebarTabContent>
                  </div>
                </div>
                <div class="layout-tabmenu-tooltip">
                  <div class="layout-tabmenu-tooltip-arrow"></div>
                  <div class="layout-tabmenu-tooltip-text">{{item.label}}</div>
                </div>
              </ng-container>
            </li>
        </ul>
    </div>
</div>
