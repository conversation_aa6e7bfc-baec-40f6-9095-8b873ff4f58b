<p-toast [style]="{marginTop: '10px'}" baseZIndex="999999" key="tb"></p-toast>
<div class="topbar clearfix">
    <a href="#" id="menu-button" (click)="appMain.onMenuButtonClick($event,0)">
      <span class="pi pi-bars"></span>
    </a>

    <a class="morpheus-logo-link" href="#">
        <img src="../../../assets/layout/images/cotwin-logo.png" class="morpheus-logo" />
    </a>

    <a id="topbar-menu-button" href="#" (click)="appMain.onTopbarMenuButtonClick($event)">
        <i class="pi pi-bars"></i>
    </a>

    <ul class="topbar-menu fadeInDown" [ngClass]="{'topbar-menu-visible': appMain.topbarMenuActive}">
      <li #search class="search-item" [ngClass]="{'active-topmenuitem':appMain.activeTopbarItem === search}">
            <span class="md-inputfield">
                <input type="text" pInputText placeholder="{{'general.search' | translate}}" (keydown.enter)="appMain.onTopbarSearchItemClick($event)">
                <i class="pi pi-search"></i>
            </span>
      </li>
        <li #translate [ngClass]="{'active-topmenuitem':appMain.activeTopbarItem === translate}">
          <a (click)="appMain.onTopbarItemClick($event,translate)">
            <p-dropdown [options]="languageService.getLanguages()"
                        optionLabel="label"
                        [ngModel]="languageService.getLanguageFromStorage()"
                        (onChange)="languageService.changeLanguage($event.value)"
                        optionValue="id">
            </p-dropdown>
          </a>
        </li>
        <li #notifications [ngClass]="{'active-topmenuitem':appMain.activeTopbarItem === notifications}">
            <a [routerLink]="notificationsRoute" (click)="appMain.onTopbarItemClick($event,notifications)">
                <i class="topbar-icon pi pi-fw pi-bell"></i>
                <span class="topbar-badge animated rubberBand">{{notificationCount}}</span>
                <span class="topbar-item-name">Notifications</span>
            </a>
        </li>
        <li #profile class="profile-item" [ngClass]="{'active-topmenuitem':appMain.activeTopbarItem === profile}">
          <a href="#" (click)="appMain.onTopbarItemClick($event,profile)">
            <div class="profile-image">
              <img [src]="imageUrl"  [className]="'avatar'" *ngIf="imageUrl">
            </div>
            <span class="profile-text">{{'general.profile' | translate}}</span>
          </a>

            <ul class="fadeInDown">
              <div class="card grid-nogutter align-content-center">
                <div class="flex justify-content-center">
                  <p-avatar size="xlarge" shape="circle" [image]="imageUrl"></p-avatar>
                </div>
                <div class="flex justify-content-center"><span class="user-card-name">{{currentUserName}}</span></div>
                <div class="flex justify-content-center"><span class="user-card-role">{{currentUserRole}}</span></div>
                <div class="flex justify-content-center"><span class="location">{{currentUserEmail}}</span></div>

              </div>

                <li role="menuitem">
                    <a [routerLink]="profileRoute">
                        <i class="pi pi-fw pi-user"></i>
                        <span>{{'general.profile' | translate}}</span>
                    </a>
                </li>
                <li *ngIf="authService.checkFeature('viewAdministrationOverview')" role="menuitem">
                    <a [routerLink]="administrationOverview" (click)="appMain.onTopbarSubItemClick($event)">
                        <i class="pi pi-fw pi-cog"></i>
                        <span>{{'general.settings' | translate}}</span>
                    </a>
                </li>
                <li role="menuitem">
                    <a href="#" (click)="authService.logout()">
                        <i class="pi pi-fw pi-sign-out"></i>
                        <span>{{'general.logout' | translate}}</span>
                    </a>
                </li>
            </ul>
        </li>
    </ul>
</div>
