import {ChangeDetector<PERSON><PERSON>, Component, <PERSON><PERSON><PERSON><PERSON>, OnInit} from '@angular/core';
import {AppComponent} from '../../app.component';
import {AppMainComponent} from './app.main.component';
import {LanguageService} from '../../services/language.service';
import {AuthService} from '../../api/services/auth.service';
import {NotificationService} from "../../api/services/notification.service";
import {NotificationsPageComponent} from "../../pages/notification/notifications-page/notifications-page.component";
import {NavigationEnd, Router} from "@angular/router";
import {
  AdministrationOverviewPageComponent
} from '../../pages/administrator/administration-overview-page/administration-overview-page.component';
import {ProfilePageComponent} from '../../pages/profile/profile-page/profile-page.component';
import {ImageService} from '../../api/services/image.service';
import {AdminUserDto} from "../../api/models/user/admin-user-dto";
import {Observable, Subject} from 'rxjs';
import {takeUntil} from 'rxjs/operators';
import {TranslateService} from '@ngx-translate/core';

@Component({
  selector: 'app-topbar',
  templateUrl: './app.topbar.component.html',
  styleUrls: ['../../../styles.css']
})
export class AppTopBarComponent implements OnInit, OnDestroy{

  public notificationsRoute;
  public administrationOverview;
  public profileRoute;
  public imageUrl: string;
  notificationCount: string = "0";
  currentUserName: string;
  currentUserEmail: string;
  currentUserRole: string;

  currentUser$: Observable<AdminUserDto>;
  public ngDestroyed$ = new Subject();

  constructor(private router: Router, public app: AppComponent, public appMain: AppMainComponent, public languageService: LanguageService,
              public authService: AuthService, public notificationService: NotificationService, private imageService: ImageService,
              private cdr: ChangeDetectorRef,
              public translateService: TranslateService
  ) {
  }

  ngOnInit(): void {
    this.currentUser$ = this.authService.getCurrentUserInfo();
    this.notificationsRoute = NotificationsPageComponent.ROUTE;
    this.profileRoute = ProfilePageComponent.ROUTE;
    this.administrationOverview = AdministrationOverviewPageComponent.ROUTE;
    this.notificationService.getNumberOfUnreadNotifications()
      .pipe(takeUntil(this.ngDestroyed$))
      .subscribe((notificationCount: string) => {
      this.notificationCount = notificationCount;
    })

    this.router.events
      .pipe(takeUntil(this.ngDestroyed$))
      .subscribe(value => {
      if (value instanceof NavigationEnd) {
        this.notificationService.setNumberOfUnreadNotifications();
      }
    })

     this.currentUser$
       .pipe(takeUntil(this.ngDestroyed$))
       .subscribe(currentUser => {
          if(currentUser){
            this.imageUrl = this.imageService.getUserImageUrl(currentUser.imageUrl);
            this.currentUserName = currentUser.firstName && currentUser.lastName ? currentUser.firstName + " " + currentUser.lastName : currentUser.login;
            this.currentUserEmail = currentUser.email ? currentUser.email : "";
            this.currentUserRole = currentUser.authorities ? (currentUser.authorities.includes('ROLE_ADMIN') ?
              this.translateService.instant('general.roles.ROLE_ADMIN') : (currentUser.authorities.includes('ROLE_ENDCUSTOMER') ?
                this.translateService.instant('general.roles.ROLE_ENDCUSTOMER') : this.translateService.instant('general.roles.ROLE_MANUFACTURER'))) : '';
          }
          this.cdr.detectChanges(); // https://stackoverflow.com/a/35106069/9479439
       })
  }

  ngOnDestroy() {
    this.ngDestroyed$.next();
  }
}
