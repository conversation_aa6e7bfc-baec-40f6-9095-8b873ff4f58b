plugins {
    id 'org.springframework.boot' version '2.7.5'
    id 'io.spring.dependency-management' version '1.1.0'
    id 'java'
    id 'com.google.cloud.tools.jib' version '3.3.1'
    id 'jacoco'
}

group = 'de.iotiq'
version = '0.0.1-SNAPSHOT'
sourceCompatibility = '17'

configurations {
    compileOnly {
        extendsFrom annotationProcessor
    }
}

repositories {
    mavenCentral()
}
subprojects{
    repositories {
        mavenCentral()
    }
    apply{
        plugin("io.spring.dependency-management")
    }
}
dependencies {
    implementation project(":logging")
    implementation project(":asset-shell")
    implementation project(":forum")
    implementation 'org.springframework.boot:spring-boot-starter-data-jpa'
    implementation 'org.springframework.boot:spring-boot-starter-security'
    implementation 'org.springframework.boot:spring-boot-starter-web'
    implementation 'org.springframework.boot:spring-boot-starter-webflux'
    implementation 'org.flywaydb:flyway-core'
    implementation 'com.sendgrid:sendgrid-java:4.9.3'
    implementation 'org.modelmapper:modelmapper:3.1.0'
    implementation 'org.hibernate.search:hibernate-search-mapper-orm:6.1.7.Final'
    implementation 'org.hibernate.search:hibernate-search-backend-elasticsearch:6.1.7.Final'
    implementation 'org.springframework.kafka:spring-kafka'
    compileOnly 'org.hibernate:hibernate-jpamodelgen'

    compileOnly 'org.projectlombok:lombok'
    runtimeOnly 'org.postgresql:postgresql'
    annotationProcessor 'org.projectlombok:lombok'
    testImplementation 'org.springframework.boot:spring-boot-starter-test'
    testImplementation 'org.springframework.security:spring-security-test'
// https://mvnrepository.com/artifact/org.zalando/problem
    implementation 'org.zalando:problem:0.27.1'
    implementation "org.zalando:problem-spring-web:0.27.0"
    implementation "io.micrometer:micrometer-registry-prometheus"
// https://mvnrepository.com/artifact/io.jsonwebtoken/jjwt-api
    implementation 'io.jsonwebtoken:jjwt-api:0.11.5'
    if (!project.hasProperty("gae")) {
        runtimeOnly "io.jsonwebtoken:jjwt-impl:0.11.5"
        runtimeOnly "io.jsonwebtoken:jjwt-jackson:0.11.5"
    } else {
        implementation "io.jsonwebtoken:jjwt-impl:0.11.5"
        implementation "io.jsonwebtoken:jjwt-jackson:0.11.5"
    }
    implementation "org.apache.commons:commons-lang3"
    implementation 'org.apache.commons:commons-text:1.10.0'
    implementation "com.fasterxml.jackson.datatype:jackson-datatype-hibernate5"
    implementation "org.springframework.boot:spring-boot-starter-mail"
    implementation "org.springframework.boot:spring-boot-starter-thymeleaf"
    implementation "org.hibernate.validator:hibernate-validator"
    implementation ("org.springdoc:springdoc-openapi-webmvc-core:1.7.0")
    implementation 'org.springdoc:springdoc-openapi-ui:1.6.14'
    implementation "org.mapstruct:mapstruct:1.5.3.Final"
    annotationProcessor "org.mapstruct:mapstruct-processor:1.5.3.Final"
    annotationProcessor 'org.hibernate:hibernate-jpamodelgen'

    implementation 'org.hibernate:hibernate-ehcache'
    testRuntimeOnly 'com.h2database:h2'
    // https://mvnrepository.com/artifact/com.nimbusds/nimbus-jose-jwt
    implementation 'com.nimbusds:nimbus-jose-jwt:9.31'

    implementation platform('org.testcontainers:testcontainers-bom:1.17.6') //import bom
    testImplementation('org.testcontainers:elasticsearch') //no version specified
    testImplementation 'org.testcontainers:junit-jupiter'
}

tasks.named('test') {
    useJUnitPlatform()
}

jib {
    from {
        image = 'eclipse-temurin:17.0.2_8-jre'
    }
    to {
        image = 'iotiqdevops/cotwin-backend'
        auth {
            username = System.getenv("DOCKERHUB_USERNAME") ?: "username1"
            password = System.getenv("DOCKERHUB_PASSWORD") ?: "pass"
        }
    }
}
